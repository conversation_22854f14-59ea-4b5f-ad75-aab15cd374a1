{"marketplace": {"title": "Marketplace", "description": "Manage products and transactions in the marketplace", "noProducts": "No products found.", "errorTitle": "An error occurred", "search": "Search products...", "filter": "Filter", "sort": "Sort", "categoriesLabel": "Categories", "allCategories": "All Categories", "price": "Price", "rating": "Rating", "loadMore": "Load More", "activeCategory": "Category:", "routes": {"marketplace": "Marketplace", "productDetail": "Product Details", "cart": "Shopping Cart", "productsForSale": "Products for Sale", "editProduct": "Edit Product", "purchasedProducts": "Purchased Products"}, "marketplace": {"title": "Marketplace", "description": "Explore and shop for products in the marketplace", "totalProducts": "Total Products", "browse": "Browse Products"}, "productsForSale": {"title": "Products for Sale", "description": "Manage the products you are selling in the marketplace", "totalProducts": "Products for Sale", "manage": "Manage Products", "addProduct": "Add Product", "editProduct": "Edit Product", "confirmDelete": "Are you sure you want to delete this product?", "submitForApproval": "Submit for Approval", "cancelSubmission": "Cancel Submission", "edit": {"title": "Edit Product"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "draft": "Draft", "deleted": "Deleted"}, "table": {"image": "Product Image", "name": "Product Name", "soldCount": "Sold", "createdAt": "Created Date", "category": "Category", "status": "Status", "price": "Price", "actions": "Actions"}, "categories": {"agent": "AI Agent", "knowledgeFile": "Knowledge File", "function": "Function", "finetune": "Fine-tune", "strategy": "Strategy", "marketing": "Marketing", "content": "Content", "seo": "SEO", "social": "Social Media", "analytics": "Analytics", "other": "Other"}, "error": {"loadDataTitle": "Error Loading Data", "retry": "Retry"}, "delete": {"confirmTitle": "Confirm Delete", "confirmMessage": "Are you sure you want to delete product \"{{name}}\"?", "deleting": "Deleting..."}, "form": {"name": "Product Name", "namePlaceholder": "Enter product name", "description": "Description", "descriptionPlaceholder": "Enter product description", "image": "Product Image", "imagePlaceholder": "Upload product image", "imageSupport": "Support multiple images, formats: JPG, PNG", "selectedImages": "Selected {{count}} new images", "quantity": "Quantity", "quantityPlaceholder": "Enter quantity", "price": "Price (VND)", "pricePlaceholder": "Enter price", "category": "Category", "categoryPlaceholder": "Select category", "status": "Status", "statusPlaceholder": "Select status", "validation": {"nameRequired": "Product name is required", "priceNonNegative": "Price cannot be negative", "categoryRequired": "Category is required", "imageRequired": "Please select at least one product image"}}}, "purchasedProducts": {"title": "Purchased Products", "description": "View and manage the products you have purchased", "totalProducts": "Purchased Products", "view": "View Products", "noName": "No name", "unknownSeller": "Unknown", "table": {"product": "Product", "seller": "<PERSON><PERSON>", "purchaseDate": "Purchase Date", "quantity": "Quantity", "price": "Purchase Price", "total": "Total", "actions": "Actions"}, "actions": {"viewDetail": "View Details", "download": "Download"}, "empty": {"title": "No products purchased yet", "description": "Explore the marketplace to find suitable products"}, "error": {"loadDataTitle": "Error Loading Data", "retry": "Retry"}, "categories": {"marketing": "Marketing", "content": "Content", "seo": "SEO", "social": "Social Media", "analytics": "Analytics", "other": "Other"}, "status": {"completed": "Completed", "processing": "Processing", "cancelled": "Cancelled"}}, "categories": {"agent": "AI Agent", "knowledgeFile": "Knowledge File", "function": "Function", "finetune": "Fine-tuned Model", "strategy": "Strategy"}, "products": {"title": "Products", "aiAssistants": "AI Assistants", "specializedAgents": "Specialized Agents", "resetFilters": "Reset Filters"}, "common": {"all": "All", "active": "Active", "draft": "Draft", "inactive": "Inactive", "cancel": "Cancel", "save": "Save", "create": "Create", "submitting": "Submitting...", "submitForApproval": "Submit for Approval"}, "product": {"addNew": "Add New Product", "basicInfo": "Basic Information", "priceInfo": "Price Information", "mediaTypes": "Files & Images", "category": {"KNOWLEDGE_FILE": "Knowledge File", "AGENT": "AI Agent", "OTHER": "Other"}, "form": {"name": "Product Name", "namePlaceholder": "Enter product name", "description": "Description", "descriptionPlaceholder": "Enter product description", "category": "Product Category", "sourceId": "Source ID", "sourceIdPlaceholder": "Search and select source", "listedPrice": "Listed Price", "discountedPrice": "Discounted Price", "images": "Product Images", "imagesHelp": "Supported formats: JPG, PNG", "selectImages": "Select Images", "userManual": "User Manual", "userManualHelp": "Supported formats: PDF", "selectFiles": "Select Files", "selectedFiles": "Selected Files", "detail": "Detail Document", "detailHelp": "Supported formats: PDF", "selectFile": "Select File", "changeFile": "Change File", "price": "Price (rpoint)", "pricePlaceholder": "Enter price (leave empty if no change)", "status": "Status", "statusPlaceholder": "Select status", "imageUpload": "Upload Images", "imageUploadHelp": "Supported formats: JPG, PNG, GIF", "detailUpload": "Upload Detail Document", "userManualUpload": "Upload User Manual", "uploadProgress": "Uploading...", "uploadSuccess": "Upload successful", "uploadError": "Upload error", "fileSelected": "File selected: {{fileName}}", "noFileSelected": "No file selected", "changeImage": "Change Image", "removeImage": "Remove Image", "additionalInfo": "Additional Information", "productContent": "Product Content", "detailPlaceholderEdit": "Enter detailed product information (if changed)", "detailPlaceholderCreate": "Enter detailed product information", "userManualPlaceholderEdit": "Enter product user manual (if changed)", "userManualPlaceholderCreate": "Enter product user manual", "categoryPlaceholder": "Select category"}, "validation": {"nameMin": "Product name must be at least 3 characters", "nameMax": "Product name must not exceed 500 characters", "descriptionRequired": "Product description is required", "listedPriceRequired": "Listed price is required", "listedPriceMin": "Listed price cannot be negative", "discountedPriceRequired": "Discounted price is required", "discountedPriceMin": "Discounted price cannot be negative", "categoryRequired": "Product category is required", "sourceIdRequired": "Source ID is required"}, "detail": {"addToCart": "Add to Cart", "buyNow": "Buy Now", "quantityLabel": "Quantity", "inStock": "In Stock", "outOfStock": "Out of Stock", "information": "Detailed Information", "specifications": "Specifications", "usage": "Usage Guide", "usageDescription": "Detailed instructions on how to use this product.", "usageStep1": "Step 1: Activate Trigger Workflow", "usageStep2": "Step 2: Get audio file", "usageStep3": "Step 3: Send audio for processing", "usageStep4": "Step 4: Return Content", "usageStep5": "Step 5: Save Content to Google Docs", "usageStep6": "Step 6: Confirm completion", "reviews": "Reviews", "relatedProducts": "Related Products", "viewDetail": "View Details", "notFound": "Product information not found"}, "purchased": "Purchased", "formulas": {"title": "Applied Formulas", "4p": "4P Formula", "4pDescription": "The 4P formula helps build effective marketing strategies.", "7a": "7A MODEL", "7aDescription": "The 7A model helps optimize marketing and sales processes.", "app": "APP FORMULA", "appDescription": "The APP formula helps build effective marketing applications."}, "level": "Level {{level}}"}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "emptyMessage": "Please add products to your cart to continue shopping.", "suggestion": "Suggestions for you", "suggestionText": "Explore featured products or search for your favorite items.", "checkout": "Checkout", "summary": "Checkout Summary", "product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "totalItems": "Total Items", "subtotal": "Subtotal", "discount": "Discount", "actions": "Actions", "remove": "Remove", "continueShopping": "Continue Shopping", "addSuccess": "Added to <PERSON><PERSON> Successfully", "viewCartMessage": "Product has been added to your cart", "viewCart": "View Cart"}, "notification": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "tryAgain": "Please try again.", "product": {"createSuccess": "Product \"{{name}}\" has been created successfully!", "createError": "Unable to create product. {{error}}", "updateSuccess": "Product \"{{name}}\" has been updated successfully!", "updateError": "Unable to update product. {{error}}", "deleteSuccess": "Product \"{{name}}\" has been deleted successfully!", "deleteSuccessGeneric": "Product has been deleted successfully!", "deleteError": "Unable to delete product. {{error}}", "batchDeleteSuccess": "Successfully deleted {{count}} products!", "batchDeleteError": "Unable to delete products. {{error}}", "submitForApprovalSuccess": "Product \"{{name}}\" has been submitted for approval!", "submitForApprovalError": "Unable to submit product for approval. {{error}}", "cancelSubmissionSuccess": "Cancelled submission for product \"{{name}}\"!", "cancelSubmissionError": "Unable to cancel submission. {{error}}"}}, "bulkActions": {"selectedItems": "{{count}} items selected", "clearSelection": "Clear all selection", "bulkDelete": "Bulk Delete", "confirmBulkDelete": "Confirm Bulk Delete", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected products?", "selectProductsToDelete": "Please select at least one product to delete"}}}