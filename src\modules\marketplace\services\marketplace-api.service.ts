import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Base API paths cho marketplace
 */
const USER_API_PATH = '/user/marketplace';

/**
 * Interface cho Product từ backend API
 */
export interface ApiProduct {
  id: number;
  name: string;
  description: string;
  listedPrice: number;
  discountedPrice: number;
  category: 'AGENT' | 'KNOWLEDGE_FILE' | 'FUNCTION' | 'FINETUNE' | 'STRATEGY';
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'DELETED';
  images: Array<{ key: string; position: number; url: string }>;
  seller: {
    id?: number;
    name: string;
    avatar: string;
    email?: string;
    phoneNumber?: string;
    type: 'user' | 'employee';
  };
  createdAt: number;
  soldCount: number;
  canPurchase: boolean;
  userManual?: string;
  detail?: string;
  sourceId: string;
}

/**
 * Interface cho Product Detail từ backend API
 * Hiện tại giống với ApiProduct, có thể mở rộng trong tương lai
 */
export type ApiProductDetail = ApiProduct;

/**
 * Interface cho Cart Item từ backend API
 */
export interface ApiCartItem {
  cartItemId: number;
  productId: number;
  productName: string;
  discountedPrice: number;
  quantity: number;
  sellerName: string;
  createdAt: number;
}

/**
 * Interface cho Cart từ backend API
 */
export interface ApiCart {
  items: ApiCartItem[];
  totalValue: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho Purchase History Item
 */
export interface ApiPurchaseHistoryItem {
  orderLineId: number;
  productId: number;
  productName: string;
  discountedPrice: number;
  quantity: number;
  sellerName: string;
  createdAt: number;
}

/**
 * Query parameters cho products
 */
export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  minPrice?: number;
  maxPrice?: number;
}

/**
 * Query parameters cho user products
 */
export interface UserProductQueryParams extends ProductQueryParams {
  status?: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'DELETED';
}

/**
 * DTO cho tạo sản phẩm
 */
export interface CreateProductDto {
  name: string;
  description: string;
  listedPrice: number;
  discountedPrice: number;
  category: 'AGENT' | 'KNOWLEDGE_FILE' | 'FUNCTION' | 'FINETUNE' | 'STRATEGY';
  sourceId: string;
  imagesMediaTypes?: string[];
  userManualMediaType?: string;
  detailMediaType?: string;
}

/**
 * DTO cho thông tin sản phẩm trong update
 */
export interface ProductInfoDto {
  name: string;
  listedPrice: number; // Backend yêu cầu bắt buộc
  discountedPrice: number; // Backend yêu cầu bắt buộc
  description?: string; // Optional theo backend
  // Note: User DTO không có category field, chỉ admin DTO mới có
}

/**
 * DTO cho thao tác ảnh
 */
export interface ImageOperationDto {
  operation: 'ADD' | 'DELETE';
  key?: string;
  index?: number;
  mimeType?: string;
}

/**
 * DTO cho cập nhật sản phẩm (format mới theo backend)
 */
export interface UpdateProductDto {
  productInfo?: ProductInfoDto; // Optional theo user DTO
  images?: ImageOperationDto[]; // Optional theo user DTO
  detailEdited?: boolean; // Optional theo user DTO
  userManual?: boolean; // Optional theo user DTO
  updateOption?: 'SAVE_DRAFT' | 'SUBMIT_FOR_APPROVAL'; // Optional theo user DTO
}

/**
 * DTO cho presigned URL của ảnh
 */
export interface PresignedUrlImageDto {
  index: number;
  uploadUrl: string;
}

/**
 * Response cho create product
 */
export interface CreateProductResponse {
  product: ApiProductDetail;
  presignedUrlImage?: PresignedUrlImageDto[];
  presignedUrlDetail?: string | null;
  presignedUrlUserManual?: string | null;
}

/**
 * Response cho update product
 */
export interface UpdateProductResponse {
  product: ApiProductDetail;
  presignedUrlImage?: PresignedUrlImageDto[];
  presignedUrlDetail?: string | null;
  presignedUrlUserManual?: string | null;
  publishError?: string;
}

/**
 * DTO cho thêm vào giỏ hàng
 */
export interface AddToCartDto {
  productId: number;
  quantity: number;
}

/**
 * DTO cho cập nhật cart item
 */
export interface UpdateCartItemDto {
  quantity: number;
}

/**
 * DTO cho thanh toán
 */
export interface PaymentDto {
  productIds: number[];
}

/**
 * Response cho thanh toán
 */
export interface PaymentResponse {
  orderId: number;
  totalPoint: number;
  remainingBalance: number;
  createdAt: number;
}

/**
 * Service chính cho Marketplace API
 */
export const MarketplaceApiService = {
  /**
   * === PRODUCT APIs ===
   */

  /**
   * Lấy danh sách sản phẩm đã được phê duyệt (public)
   */
  getApprovedProducts: async (params?: ProductQueryParams): Promise<PaginatedResult<ApiProduct>> => {
    const response = await apiClient.get<PaginatedResult<ApiProduct>>(
      `${USER_API_PATH}/products/approved`,
      { params }
    );
    return response.result;
  },

  /**
   * Lấy chi tiết sản phẩm
   */
  getProductDetail: async (productId: number): Promise<ApiProductDetail> => {
    try {
      console.log('🔍 [MarketplaceApiService] Fetching product detail for ID:', productId);
      console.log('🔍 [MarketplaceApiService] API URL:', `${USER_API_PATH}/products/detail/${productId}`);

      const response = await apiClient.get<ApiProductDetail>(
        `${USER_API_PATH}/products/detail/${productId}`
      );

      console.log('✅ [MarketplaceApiService] Product detail response:', response);
      console.log('✅ [MarketplaceApiService] Product detail result:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error fetching product detail:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/products/detail/${productId}`,
        productId
      });
      throw error;
    }
  },

  /**
   * Lấy danh sách sản phẩm của user hiện tại
   */
  getUserProducts: async (params?: UserProductQueryParams): Promise<PaginatedResult<ApiProduct>> => {
    const response = await apiClient.get<PaginatedResult<ApiProduct>>(
      `${USER_API_PATH}/products`,
      { params }
    );
    return response.result;
  },

  /**
   * Lấy chi tiết sản phẩm của user
   */
  getUserProductDetail: async (productId: number): Promise<ApiProductDetail> => {
    const response = await apiClient.get<ApiProductDetail>(
      `${USER_API_PATH}/products/${productId}`
    );
    return response.result;
  },

  /**
   * Tạo sản phẩm mới
   */
  createProduct: async (data: CreateProductDto): Promise<CreateProductResponse> => {
    try {

      const response = await apiClient.post<CreateProductResponse>(
        `${USER_API_PATH}/products`,
        data
      );


      return response.result;
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error creating product:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/products`,
        requestData: data
      });
      throw error;
    }
  },

  /**
   * Cập nhật sản phẩm
   */
  updateProduct: async (productId: number, data: UpdateProductDto): Promise<UpdateProductResponse> => {
    try {
      console.log('🔍 [MarketplaceApiService] Updating product ID:', productId);
      console.log('🔍 [MarketplaceApiService] Update data:', data);
      console.log('🔍 [MarketplaceApiService] API URL:', `${USER_API_PATH}/products/${productId}`);

      const response = await apiClient.put<UpdateProductResponse>(
        `${USER_API_PATH}/products/${productId}`,
        data
      );

      console.log('✅ [MarketplaceApiService] Update response:', response);
      console.log('✅ [MarketplaceApiService] Updated product:', response.result);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error updating product:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/products/${productId}`,
        requestData: data
      });
      throw error;
    }
  },

  /**
   * Gửi sản phẩm để duyệt
   */
  submitProductForApproval: async (productId: number): Promise<ApiProductDetail> => {
    const response = await apiClient.post<ApiProductDetail>(
      `${USER_API_PATH}/products/${productId}/pending`
    );
    return response.result;
  },

  /**
   * Hủy gửi duyệt sản phẩm
   */
  cancelProductSubmission: async (productId: number): Promise<ApiProductDetail> => {
    const response = await apiClient.post<ApiProductDetail>(
      `${USER_API_PATH}/products/${productId}/cancel-submission`
    );
    return response.result;
  },

  /**
   * Xóa sản phẩm
   */
  deleteProduct: async (productId: number): Promise<void> => {
    try {
      console.log('🔍 [MarketplaceApiService] Deleting product ID:', productId);
      console.log('🔍 [MarketplaceApiService] Delete URL:', `${USER_API_PATH}/products/${productId}`);

      await apiClient.delete(`${USER_API_PATH}/products/${productId}`);

      console.log('✅ [MarketplaceApiService] Product deleted successfully');
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error deleting product:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/products/${productId}`,
        productId
      });
      throw error;
    }
  },

  /**
   * Xóa nhiều sản phẩm
   */
  batchDeleteProducts: async (productIds: number[]): Promise<void> => {
    try {
      console.log('🔍 [MarketplaceApiService] Batch deleting products:', productIds);
      console.log('🔍 [MarketplaceApiService] Batch delete URL:', `${USER_API_PATH}/products/batch`);

      await apiClient.delete(`${USER_API_PATH}/products/batch`, {
        data: { productIds }
      });

      console.log('✅ [MarketplaceApiService] Products batch deleted successfully');
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error batch deleting products:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/products/batch`,
        productIds
      });
      throw error;
    }
  },

  /**
   * === CART APIs ===
   */

  /**
   * Lấy giỏ hàng hiện tại
   */
  getCart: async (): Promise<ApiCart> => {
    const response = await apiClient.get<ApiCart>(`${USER_API_PATH}/cart`);
    return response.result;
  },

  /**
   * Thêm sản phẩm vào giỏ hàng
   */
  addToCart: async (data: AddToCartDto): Promise<ApiCart> => {
    const response = await apiClient.post<ApiCart>(`${USER_API_PATH}/cart`, data);
    return response.result;
  },

  /**
   * Cập nhật số lượng sản phẩm trong giỏ hàng
   */
  updateCartItem: async (cartItemId: number, data: UpdateCartItemDto): Promise<ApiCart> => {
    const response = await apiClient.put<ApiCart>(`${USER_API_PATH}/cart/${cartItemId}`, data);
    return response.result;
  },

  /**
   * Xóa sản phẩm khỏi giỏ hàng
   */
  removeCartItem: async (cartItemId: number): Promise<ApiCart> => {
    const response = await apiClient.delete<ApiCart>(`${USER_API_PATH}/cart/${cartItemId}`);
    return response.result;
  },

  /**
   * === PAYMENT APIs ===
   */

  /**
   * Thanh toán sản phẩm
   */
  processPayment: async (data: PaymentDto): Promise<PaymentResponse> => {
    const response = await apiClient.post<PaymentResponse>(`${USER_API_PATH}/payment`, data);
    return response.result;
  },

  /**
   * === ORDER APIs ===
   */

  /**
   * Lấy lịch sử mua hàng
   */
  getPurchaseHistory: async (params?: Record<string, unknown>): Promise<PaginatedResult<ApiPurchaseHistoryItem>> => {
    try {
      console.log('🔍 [MarketplaceApiService] Calling purchase history API with params:', params);
      console.log('🔍 [MarketplaceApiService] API URL:', `${USER_API_PATH}/orders/purchase-history`);

      const response = await apiClient.get<PaginatedResult<ApiPurchaseHistoryItem>>(`${USER_API_PATH}/orders/purchase-history`, { params });

      console.log('✅ [MarketplaceApiService] Purchase history API response:', response);
      console.log('✅ [MarketplaceApiService] Purchase history result:', response.result);
      console.log('✅ [MarketplaceApiService] Purchase history items:', response.result?.items);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ [MarketplaceApiService] Error fetching purchase history:', error);
      console.error('❌ [MarketplaceApiService] Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${USER_API_PATH}/orders/purchase-history`,
        params
      });
      throw error;
    }
  },
};
