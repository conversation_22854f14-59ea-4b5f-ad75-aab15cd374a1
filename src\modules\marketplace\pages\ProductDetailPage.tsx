import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Alert,
  Button,
  Card,
  Chip,
  Icon,
  Loading,
  ResponsiveGrid,
  Typography,
} from '@/shared/components/common';
import { useProductDetail } from '../hooks/useProductDetail';
import { useCartManager } from '../hooks/useCartApi';
import { ProductGallery } from '..';
import QuantityInput from '../components/QuantityInput';
import ProductDetailInfo from '../components/ProductDetailInfo';
import ProductUsageGuide from '../components/ProductUsageGuide';
import RelatedProducts from '../components/RelatedProducts';

/**
 * Trang chi tiết sản phẩm
 */
const ProductDetailPage: React.FC = () => {
  const { productId = '1' } = useParams<{ productId: string }>();
  const { t } = useTranslation(['marketplace', 'common']);
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);

  console.log('🔍 [ProductDetailPage] Product ID from URL:', productId);

  // Lấy thông tin chi tiết sản phẩm
  const { data: product, isLoading, error } = useProductDetail(productId);

  console.log('🔍 [ProductDetailPage] Product data:', product);
  console.log('🔍 [ProductDetailPage] Product images:', product?.images);
  console.log('🔍 [ProductDetailPage] Loading state:', isLoading);
  console.log('🔍 [ProductDetailPage] Error state:', error);

  // Sử dụng Cart API
  const { addToCart, isAddingToCart } = useCartManager();

  // Xử lý khi thêm vào giỏ hàng
  const handleAddToCart = () => {
    if (product) {
      // Thêm vào giỏ hàng với API
      addToCart({ productId: product.id, quantity });
    }
  };

  // Xử lý khi mua ngay
  const handleBuyNow = () => {
    if (product) {
      // Thêm vào giỏ hàng và chuyển đến trang thanh toán
      addToCart({ productId: product.id, quantity });

      // Chuyển hướng đến trang giỏ hàng
      setTimeout(() => {
        navigate('/marketplace/cart');
      }, 500);
    }
  };

  // Format giá tiền
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị lỗi
  if (error || !product) {
    return (
      <Alert
        type="error"
        title={t('common:error.title', 'Đã xảy ra lỗi')}
        message={t('marketplace:product.detail.notFound', 'Không tìm thấy thông tin sản phẩm')}
      />
    );
  }

  return (
    <div>
      {/* Thông tin sản phẩm */}
      <Card className="mb-8">
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
          gap={{ xs: 6, md: 8 }}
          className="p-4"
        >
          {/* Cột trái: Hình ảnh sản phẩm */}
          <ProductGallery product={product} useTestImages={false} useTestVideos={false} />

          {/* Cột phải: Thông tin sản phẩm */}
          <div className="flex flex-col">
            {/* Tag danh mục */}
            <div className="mb-2">
              <Chip valiran className="inline-block px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-full">
                {product.categoryDisplay?.name || 'Sản phẩm'}
              </Chip>
            </div>

            {/* Tên sản phẩm */}
            <Typography variant="h4" className="mb-4">
              {product.name}
            </Typography>

            {/* Mô tả ngắn */}
            <Typography variant="body1" className="mb-6 text-gray-600">
              {product.description}
            </Typography>

            {/* Giá */}
            <div className="mb-6 flex items-center">
              <div className="flex items-center">
                <Typography variant="h3" className="text-red-600 font-bold mr-2">
                  {formatPrice(product.price || product.discountedPrice)}
                </Typography>
                <Icon name="rpoint" size="md" />
              </div>
              {product.originalPrice && (
                <Typography variant="body1" color="muted" className="line-through mt-1 ml-1 ">
                  {formatPrice(product.originalPrice)}
                </Typography>
              )}
            </div>

            {/* Số lượng */}
            <div className="mb-6">
              <QuantityInput
                value={quantity}
                onChange={setQuantity}
                min={1}
                max={product.stockQuantity || 999}
              />
            </div>

            {/* Buttons */}
            <div className="flex flex-wrap gap-4 mt-auto">
              <Button
                variant="outline"
                onClick={handleAddToCart}
                isLoading={isAddingToCart}
                className="flex-1 py-3"
              >
                {t('marketplace:product.detail.addToCart', 'Thêm giỏ hàng')}
              </Button>

              <Button
                variant="primary"
                onClick={handleBuyNow}
                isLoading={isAddingToCart}
                className="flex-1 "
              >
                {t('marketplace:product.detail.buyNow', 'Mua ngay')}
              </Button>
            </div>
          </div>
        </ResponsiveGrid>
      </Card>

      {/* Thông tin chi tiết và hướng dẫn sử dụng */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
        gap={4}
        className="mb-6"
      >
        <RelatedProducts product={product} defaultOpen={true} />
        <ProductDetailInfo product={product} defaultOpen={true} />

        <ProductUsageGuide product={product} defaultOpen={false} />
      </ResponsiveGrid>
    </div>
  );
};

export default ProductDetailPage;
