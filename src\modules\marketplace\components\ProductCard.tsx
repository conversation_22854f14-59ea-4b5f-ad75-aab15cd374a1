import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Icon, ResponsiveImage, Avatar, Chip } from '@/shared/components/common';
import { ProductListItem } from '../types/product.types';
import { useTheme } from '@/shared/contexts';

export interface ProductCardProps {
  /**
   * Dữ liệu sản phẩm
   */
  product: ProductListItem;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback khi click vào category
   */
  onCategoryClick?: (categorySlug: string, e: React.MouseEvent) => void;
}

/**
 * Component hiển thị card sản phẩm với thumbnail, tên, giá và thông tin khác
 */
const ProductCard: React.FC<ProductCardProps> = ({ product, className = '', onCategoryClick }) => {
  const navigate = useNavigate();
  // const { t } = useTranslation('marketplace');
  useTheme(); // Sử dụng hook theme

  // Xử lý khi click vào category
  const handleCategoryClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onCategoryClick && product.category) {
      onCategoryClick(product.category, e);
    } else if (product.category) {
      navigate(`/marketplace/category/${product.category}`);
    }
  };

  // Format giá tiền
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Xử lý khi click vào card
  const handleCardClick = () => {
    navigate(`/marketplace/product/${product.id}`);
  };

  return (
    <Card
      className={`group cursor-pointer transition-all duration-300 ease-in-out hover:shadow-xl hover:-translate-y-2 border-0 ${className}`}
      noPadding={true}
      allowOverflow={false}
      style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
      onClick={handleCardClick}
    >
      {/* Product thumbnail */}
      <div className="relative w-full">
        <div className="w-full h-56 overflow-hidden rounded-t-xl bg-gradient-to-br from-gray-100 to-gray-200">
          <ResponsiveImage
            src={product.thumbnail || '/placeholder-image.jpg'}
            alt={product.name}
            className="h-full w-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
            aspectRatio="16/9"
            lazy={true}
            style={{ display: 'block' }}
          />

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        

        {/* Category badge */}
        <div className="absolute top-3 left-3">
          <div onClick={handleCategoryClick} className="z-10">
            <Chip variant="primary" size="sm">
              {product.category}
            </Chip>
          </div>
        </div>
      </div>

      {/* Product content */}
      <div className="p-5 flex-grow flex flex-col">
        {/* Title */}
        <Typography
          variant="h6"
          className="text-lg font-bold line-clamp-2 text-gray-900 dark:text-white group-hover:text-primary transition-colors mb-3 leading-tight"
        >
          {product.name}
        </Typography>

        {/* Seller info */}
        <div className="flex items-center mb-4">
          <Avatar
            src={product.seller.avatar}
            alt={product.seller.name}
            size="sm"
            className="mr-3 ring-2 ring-gray-100"
          />
          <div className="flex-1 min-w-0">
            <Typography
              variant="body2"
              className="text-sm font-medium text-gray-700 dark:text-gray-300 truncate"
            >
              {product.seller.name}
            </Typography>
            <Typography variant="caption" className="text-xs text-gray-500 dark:text-gray-400">
              Đã bán{' '}
              {product.soldCount ? new Intl.NumberFormat('vi-VN').format(product.soldCount) : '0'}
            </Typography>
          </div>
        </div>

        {/* Price section */}
        <div className="mt-auto">
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              {/* {product.originalPrice &&
                product.originalPrice > (product.price || product.discountedPrice || 0) && (
                  <Typography variant="caption" className="text-sm text-gray-500 line-through">
                    {formatPrice(product.originalPrice)}
                  </Typography>
                )} */}
              <div className="flex items-center">
                <Typography variant="h5" className="text-xl font-bold text-red-600">
                  {formatPrice(product.price || product.discountedPrice || 0)}
                </Typography>
                <Icon name="rpoint" size="sm" className="ml-1 text-red-600" />
              </div>
            </div>

            {/* Action button */}
            <div className="flex items-center">
              {!product.canPurchase ? (
                <div className="w-10 h-10 rounded-full bg-[#ff3333] flex items-center justify-center">
                  <Icon name="check" size="sm" className="text-white" />
                </div>
              ) : (
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <Icon name="shopping-cart" size="sm" className="text-white" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ProductCard;
